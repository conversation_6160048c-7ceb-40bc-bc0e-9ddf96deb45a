import 'package:flutter/material.dart';
import '../models/meal_log.dart' as meal_log_models show MealLog;
import '../models/meal.dart' show MealDetail;
import '../services/navigation_service.dart';
import '../screens/meal/meal_completion_screen.dart';
import '../utils/image_utils.dart';
import '../utils/logger.dart';
// import 'dart:developer' as developer; // Import developer for logging (removed)

class MealCardWidget extends StatelessWidget {
  final meal_log_models.MealLog mealLog;
  final DateTime? date;
  final Function? onMealCompleted;

  const MealCardWidget({
    Key? key,
    required this.mealLog,
    required this.date,
    this.onMealCompleted,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Get MealDetail from MealLog
    final MealDetail meal = mealLog.meal;

    // TEMP DEBUG: Log that meal card is being built
    Logger.tempLog('BUILDING_MEAL_CARD: ${meal.name} - ${meal.imageUrl}',
        tag: 'MealCardWidget');

    // Use data from MealDetail
    final String mealName = meal.name;
    final int calories = meal.calories ?? 0;
    // Cook time is hardcoded in the UI as 20min
    final int protein = (meal.protein ?? 0.0).toInt();
    final int fat = (meal.fat ?? 0.0).toInt();
    final String? imageUrl = meal.imageUrl;
    final bool isCompleted = mealLog.isCompleted;

    // Log the image URL
    // Removed log

    // Wrap the card content in a GestureDetector
    return GestureDetector(
      onTap: () async {
        Logger.debug(
            'Meal card tapped: ${mealLog.meal.name}, Completed: $isCompleted, Date: $date',
            tag: 'MealCardWidget');
        // If meal is completed, navigate to MealCompletionScreen instead of MealDetailsScreen
        if (isCompleted) {
          // Import the MealCompletionScreen
          await NavigationService.navigateTo(
            MealCompletionScreen(mealLog: mealLog),
          );
        } else {
          // For non-completed meals, use the original navigation
          await NavigationService.navigateToNamed(
            NavigationService.mealDetails,
            arguments: {
              'mealLog': mealLog,
              'date': date,
            },
          );
          // Always call onMealCompleted when returning from meal details
          // This ensures calorie data gets refreshed even if the user just views the meal
          if (onMealCompleted != null) {
            Logger.debug('Calling onMealCompleted callback from MealCardWidget',
                tag: 'MealCardWidget');
            onMealCompleted!();
          }
        }
      },
      child: Container(
        width: 286,
        height: 236,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          // No shadows to keep it clean
        ),
        clipBehavior: Clip.antiAlias, // Smoother clipping for rounded corners
        child: Stack(
          fit: StackFit.expand, // Make sure stack fills the container
          children: [
            // Background Image - Full container
            imageUrl != null && imageUrl.isNotEmpty
                ? ImageUtils.getNetworkImageWithFallback(
                    imageUrl: imageUrl,
                    width: double.infinity,
                    height: double.infinity,
                    fit: BoxFit.cover,
                    fallbackIcon: Icons.restaurant_menu,
                    fallbackIconColor: Colors.grey.shade600,
                  )
                : Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: Colors.grey[200],
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.restaurant_menu,
                            color: Colors.grey[600],
                            size: 48,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Image Unavailable',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

            // Gradient overlay - Bottom to top for better text visibility
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Color.fromRGBO(0, 0, 0, 0.7), // Dark overlay at bottom
                    Color.fromRGBO(0, 0, 0, 0.0), // Transparent at top
                  ],
                  stops: [
                    0.0,
                    0.7
                  ], // Adjust gradient stops for better visibility
                ),
              ),
            ),

            // Main content - Positioned at the top
            Positioned(
              top: 16,
              left: 16,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Protein Card
                  _InfoCard(value: '${protein}g', label: 'Protein'),

                  const SizedBox(height: 4),

                  // Fat Card
                  _InfoCard(value: '${fat}g', label: 'Fat'),

                  const SizedBox(
                      height: 36), // Slightly reduced spacing from original 48

                  // Food title and details
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        mealName,
                        style: const TextStyle(
                          fontSize: 24,
                          fontFamily: 'Work Sans',
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        maxLines: 2, // Allow two lines for long names
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(
                          height: 8), // Slightly increased from original 4
                      Row(
                        children: [
                          Text(
                            '${calories}kcal',
                            style: const TextStyle(
                              fontSize: 14,
                              fontFamily: 'Work Sans',
                              color: Colors.white70,
                            ),
                          ),
                          const SizedBox(width: 16),
                          const Text(
                            '20min',
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: 'Work Sans',
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Completion Indicator Overlay (If completed)
            if (isCompleted)
              Positioned(
                top: 12,
                right: 12,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(
                            red: 0,
                            green: 0,
                            blue: 0,
                            alpha: 40), // 0.15 opacity
                        spreadRadius: 0,
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: Colors.green.shade600,
                        size: 14,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Completed',
                        style: TextStyle(
                          color: Colors.green.shade800,
                          fontWeight: FontWeight.bold,
                          fontSize: 11,
                          fontFamily: 'Work Sans',
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _InfoCard extends StatelessWidget {
  final String value;
  final String label;

  const _InfoCard({
    Key? key,
    required this.value,
    required this.label,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 70, // Fixed width for consistent sizing
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontFamily: 'Work Sans',
              color: Color.fromRGBO(16, 17, 20, 1),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 10,
              fontFamily: 'Work Sans',
              color: Color.fromRGBO(16, 17, 20, 1),
            ),
          ),
        ],
      ),
    );
  }
}
